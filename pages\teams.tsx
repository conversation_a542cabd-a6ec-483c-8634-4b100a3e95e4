import Head from "next/head";
import { useState, useEffect, useRef } from "react";
import Link from "next/link";
import { useErrorSound } from "../utils/useErrorSound";

interface Team {
  id: number;
  name: string;
  password?: string;
  status: number;
  time: string;
  videos?: {
    unlocked: number;
    total: number;
    percentage: number;
  };
}

export default function Teams() {
  const [teams, setTeams] = useState<Team[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const cursorRef = useRef<HTMLDivElement>(null);
  const { playErrorSound } = useErrorSound();

  const fetchTeams = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/teams');
      const data = await response.json();

      if (response.ok) {
        setTeams(data.teams);
        setError(null);
      } else {
        setError(data.message || 'Chyba při načítání tý<PERSON>');
        playErrorSound(); // Přehrání zvukového efektu při erroru
      }
    } catch (error) {
      setError('Chyba při komunikaci se serverem');
      playErrorSound(); // Přehrání zvukového efektu při erroru
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchTeams();
  }, []);

  // Custom cursor movement
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (cursorRef.current) {
        cursorRef.current.style.left = `${e.clientX}px`;
        cursorRef.current.style.top = `${e.clientY}px`;
      }
    };

    const handleMouseDown = () => {
      if (cursorRef.current) {
        cursorRef.current.classList.add('active');
      }
    };

    const handleMouseUp = () => {
      if (cursorRef.current) {
        cursorRef.current.classList.remove('active');
      }
    };

    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('mousedown', handleMouseDown);
    window.addEventListener('mouseup', handleMouseUp);

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mousedown', handleMouseDown);
      window.removeEventListener('mouseup', handleMouseUp);
    };
  }, []);

  // Create matrix rain effect
  useEffect(() => {
    const matrixBg = document.querySelector('.matrix-bg');
    if (!matrixBg) return;

    // Clear any existing columns
    matrixBg.innerHTML = '';

    const characters = 'アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン0123456789';
    const columns = Math.floor(window.innerWidth / 20);

    for (let i = 0; i < columns; i++) {
      const column = document.createElement('div');
      column.className = 'matrix-column';
      column.style.left = `${i * 20}px`;
      column.style.animationDuration = `${Math.random() * 10 + 10}s`;

      const columnHeight = Math.floor(Math.random() * 50) + 10;
      for (let j = 0; j < columnHeight; j++) {
        const char = document.createElement('div');
        char.textContent = characters.charAt(Math.floor(Math.random() * characters.length));
        char.style.opacity = j === 0 ? '1' : `${Math.random() * 0.5 + 0.1}`;
        column.appendChild(char);
      }

      matrixBg.appendChild(column);
    }
  }, []);

  return (
    <>
      <Head>
        <title>BLEK.CZ | Registrované týmy</title>
        <meta name="description" content="Seznam registrovaných týmů" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>

      {/* Custom cursor */}
      <div className="cursor" ref={cursorRef}></div>

      {/* Matrix background */}
      <div className="matrix-bg"></div>

      <main>
        <div className="base teams-page">
          <div className="terminal-header">
            <div className="terminal-title">REGISTERED_TEAMS.exe</div>
            <div className="terminal-controls">
              <div className="terminal-control minimize"></div>
              <div className="terminal-control maximize"></div>
              <div className="terminal-control close"></div>
            </div>
          </div>

          <div className="logo glitch" data-text="REGISTROVANÉ TÝMY">REGISTROVANÉ TÝMY</div>

          <div className="navigation-links">
            <Link href="/" className="nav-link">
              &lt; ZPĚT NA HLAVNÍ STRÁNKU
            </Link>
          </div>

          {isLoading ? (
            <div className="loading-message">Načítání seznamu týmů...</div>
          ) : error ? (
            <div className="error-message">{error}</div>
          ) : teams.length > 0 ? (
            <div className="teams-list-container">
              <div className="teams-list">
                {teams.map((team) => (
                  <div key={team.id} className="team-item">
                    <div className="team-info">
                      <span className="team-name">{team.name}</span>
                      <span className="team-date">
                        {team.time ? new Date(team.time).toLocaleDateString('cs-CZ') : 'Datum není k dispozici'}
                      </span>
                    </div>
                    {team.videos && (
                      <div className="team-videos-info">
                        <div className="videos-progress">
                          <div
                            className="progress-bar"
                            style={{ width: `${team.videos.percentage}%` }}
                            title={`${team.videos.percentage}% dokončeno`}
                          ></div>
                        </div>
                        <span className="videos-count">
                          {team.videos.unlocked} / {team.videos.total} ({team.videos.percentage}%)
                        </span>
                      </div>
                    )}
                  </div>
                ))}
              </div>
              <div className="teams-count">
                Celkem registrováno: <span className="count">{teams.length}</span>
              </div>
            </div>
          ) : (
            <div className="no-teams-message">Žádné týmy nejsou registrovány</div>
          )}

        </div>
      </main>

      <style jsx>{`
        .teams-page {
          max-width: 800px;
        }

        .navigation-links {
          margin: 1rem 0;
          display: flex;
          justify-content: flex-start;
        }

        .nav-link {
          color: var(--primary-dim);
          font-family: 'Fira Code', monospace;
          font-size: 0.9rem;
          text-decoration: none;
          transition: color 0.2s ease;
        }

        .nav-link:hover {
          color: var(--primary);
        }

        .teams-list-container {
          width: 100%;
          margin-top: 1rem;
        }

        .teams-list {
          width: 100%;
          max-height: 500px;
          overflow-y: auto;
          background: rgba(0, 0, 0, 0.3);
          padding: 0.5rem;
          margin-bottom: 1rem;
        }

        .team-item {
          padding: 0.75rem;
          margin-bottom: 0.5rem;
          background: rgba(20, 20, 20, 0.7);
          transition: all 0.2s ease;
        }

        .team-item:hover {
          background: rgba(40, 40, 40, 0.7);
        }

        .team-info {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 0.5rem;
        }

        .team-name {
          font-family: 'Fira Code', monospace;
          color: var(--primary);
          font-weight: bold;
        }

        .team-date {
          font-family: 'Fira Code', monospace;
          color: var(--primary-dim);
          font-size: 0.8rem;
        }

        .team-videos-info {
          margin-top: 0.5rem;
        }

        .videos-progress {
          width: 100%;
          height: 6px;
          background: rgba(0, 0, 0, 0.3);
          margin-bottom: 0.5rem;
          overflow: hidden;
        }

        .progress-bar {
          height: 100%;
          background: var(--primary);
          transition: width 0.3s ease;
        }

        .videos-count {
          font-family: 'Fira Code', monospace;
          color: var(--primary-dim);
          font-size: 0.8rem;
          display: block;
          text-align: right;
        }

        .teams-count {
          font-family: 'Fira Code', monospace;
          color: var(--primary-dim);
          font-size: 0.9rem;
        }

        .count {
          color: var(--primary);
          font-weight: bold;
        }

        .loading-message, .no-teams-message, .error-message {
          font-family: 'Fira Code', monospace;
          color: var(--primary-dim);
          margin: 2rem 0;
          text-align: center;
        }

        .error-message {
          color: var(--error);
        }
      `}</style>
    </>
  );
}