import { useCallback, useRef } from 'react';

/**
 * Hook pro přehr<PERSON><PERSON>í zvukového efektu při errorech
 */
export const useErrorSound = () => {
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // Inicializace audio objektu
  const initAudio = useCallback(() => {
    if (!audioRef.current) {
      audioRef.current = new Audio('/perfect-fart.mp3');
      audioRef.current.volume = 0.5; // Nastavíme hlasitost na 50%
      audioRef.current.preload = 'auto';
    }
  }, []);

  // Funkce pro přehrání zvuku
  const playErrorSound = useCallback(() => {
    try {
      initAudio();
      if (audioRef.current) {
        // Reset audio na začátek pro případ, že už hraje
        audioRef.current.currentTime = 0;
        audioRef.current.play().catch((error) => {
          console.warn('Nepodařilo se přehr<PERSON>t zvukový efekt:', error);
        });
      }
    } catch (error) {
      console.warn('Chyba při přehrávání zvukového efektu:', error);
    }
  }, [initAudio]);

  return { playErrorSound };
};
