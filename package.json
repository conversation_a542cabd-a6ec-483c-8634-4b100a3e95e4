{"name": "game-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:db": "jest --testPathPattern=database", "test:api": "jest --testPathPattern=api", "test:utils": "jest --testPathPattern=utils", "test:scripts": "jest --testPathPattern=scripts"}, "dependencies": {"@types/mime-types": "^2.1.4", "@types/mysql": "^2.15.27", "mime-types": "^3.0.1", "mysql2": "^3.14.1", "next": "15.3.1", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@testing-library/react": "^16.3.0", "@types/jest": "^29.5.12", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "dotenv": "^16.4.5", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.0-beta.3", "node-mocks-http": "^1.14.1", "ts-jest": "^29.1.2", "typescript": "^5"}}