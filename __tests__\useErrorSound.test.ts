/**
 * @jest-environment jsdom
 */
import { renderHook } from '@testing-library/react';
import { useErrorSound } from '../utils/useErrorSound';

// Mock HTMLAudioElement
const mockPlay = jest.fn().mockResolvedValue(undefined);
const mockAudioInstances: any[] = [];

class MockAudio {
  volume = 0.5;
  currentTime = 0;
  preload = 'auto';

  play = mockPlay;

  constructor(public src: string) {
    mockAudioInstances.push(this);
  }
}

// Mock global Audio constructor
const MockAudioConstructor = jest.fn().mockImplementation((src: string) => new MockAudio(src));
global.Audio = MockAudioConstructor as any;

describe('useErrorSound', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockAudioInstances.length = 0;
  });

  test('should create audio instance with correct source', () => {
    const { result } = renderHook(() => useErrorSound());

    // Trigger audio initialization by calling playErrorSound
    result.current.playErrorSound();

    expect(MockAudioConstructor).toHaveBeenCalledWith('/perfect-fart.mp3');
  });

  test('should play error sound when called', () => {
    const { result } = renderHook(() => useErrorSound());

    result.current.playErrorSound();

    // Audio should be created and play should be called
    expect(mockPlay).toHaveBeenCalled();
  });

  test('should reset currentTime before playing', () => {
    const { result } = renderHook(() => useErrorSound());

    result.current.playErrorSound();

    // Check that audio instance was created and currentTime was reset
    expect(mockAudioInstances.length).toBe(1);
    expect(mockAudioInstances[0].currentTime).toBe(0);
  });

  test('should handle play errors gracefully', () => {
    const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
    mockPlay.mockRejectedValueOnce(new Error('Play failed'));

    const { result } = renderHook(() => useErrorSound());

    expect(() => result.current.playErrorSound()).not.toThrow();

    consoleSpy.mockRestore();
  });
});
